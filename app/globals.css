@tailwind base;
@tailwind components;
@tailwind utilities;

/* Performance optimizations */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

/* Hardware acceleration for animations */
[data-framer-motion] {
  will-change: transform, opacity;
  transform: translateZ(0);
}

/* Optimize image rendering */
img {
  content-visibility: auto;
}

/* Reduce animation complexity on lower-end devices */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Performance-focused utility classes */
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform, opacity;
}

.optimize-text {
  text-rendering: optimizeSpeed;
  font-smooth: auto;
  -webkit-font-smoothing: auto;
  -moz-osx-font-smoothing: auto;
}

/* Container queries for responsive design without JS */
@container (max-width: 768px) {
  .mobile-optimized {
    animation: none !important;
    transform: none !important;
  }
}

:root {
  --header-height: 64px;

  text-rendering: geometricprecision;
  -webkit-text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.no-scrollbar::-webkit-scrollbar {
  display: none;
}

:root {
  /* Light mode slate */
  --slate-1: 252 252 253;
  --slate-2: 249 249 251;
  --slate-3: 240 240 243;
  --slate-4: 232 232 236;
  --slate-5: 224 225 230;
  --slate-6: 217 217 224;
  --slate-7: 205 206 214;
  --slate-8: 185 187 198;
  --slate-9: 139 141 152;
  --slate-10: 128 131 141;
  --slate-11: 96 100 108;
  --slate-12: 28 32 36;

  /* Light mode gray */
  --gray-1: 252 252 252;
  --gray-2: 249 249 249;
  --gray-3: 240 240 240;
  --gray-4: 232 232 232;
  --gray-5: 224 224 224;
  --gray-6: 217 217 217;
  --gray-7: 206 206 206;
  --gray-8: 187 187 187;
  --gray-9: 141 141 141;
  --gray-10: 131 131 131;
  --gray-11: 100 100 100;
  --gray-12: 32 32 32;
}

:root:is(html.dark) {
  /* Dark mode slate */
  --slate-1: 17 17 19;
  --slate-2: 24 25 27;
  --slate-3: 33 34 37;
  --slate-4: 39 42 45;
  --slate-5: 46 49 53;
  --slate-6: 54 58 63;
  --slate-7: 67 72 78;
  --slate-8: 90 97 105;
  --slate-9: 105 110 119;
  --slate-10: 119 123 132;
  --slate-11: 176 180 186;
  --slate-12: 237 238 240;

  /* Dark mode gray */
  --gray-1: 17 17 17;
  --gray-2: 25 25 25;
  --gray-3: 34 34 34;
  --gray-4: 42 42 42;
  --gray-5: 49 49 49;
  --gray-6: 58 58 58;
  --gray-7: 72 72 72;
  --gray-8: 96 96 96;
  --gray-9: 110 110 110;
  --gray-10: 123 123 123;
  --gray-11: 180 180 180;
  --gray-12: 238 238 238;
}

:root {
  color: theme("colors.gray.1");
  background-color: theme("colors.slate.12");
}

:root:is(html.light) {
  /* Fix overlap scroll background */
  background-color: theme("colors.slate.1");
}

html,
body {
  background-color: theme("colors.slate.12");
}

/* Premium animations and effects */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

@keyframes glow {
  0%, 100% { box-shadow: 0 0 20px rgba(139, 92, 246, 0.3); }
  50% { box-shadow: 0 0 40px rgba(139, 92, 246, 0.6); }
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

.animate-shimmer {
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

/* Smooth scrollbar for modal content */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgb(var(--slate-3));
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgb(var(--slate-6));
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgb(var(--slate-8));
}

/* Premium glass morphism effect */
.glass-morphism {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Gradient text effect */
.gradient-text {
  background: linear-gradient(135deg, #8b5cf6, #ec4899, #f59e0b);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Custom focus styles */
.focus-ring:focus {
  outline: 2px solid rgb(139, 92, 246);
  outline-offset: 2px;
}

/* Button hover effects */
.btn-premium {
  background: linear-gradient(135deg, #8b5cf6, #ec4899);
  position: relative;
  overflow: hidden;
}

.btn-premium::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.btn-premium:hover::before {
  left: 100%;
}

/* Pulse animation for notifications */
@keyframes pulse-soft {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.8; transform: scale(1.05); }
}

.animate-pulse-soft {
  animation: pulse-soft 2s ease-in-out infinite;
}

/* Custom modal backdrop */
.modal-backdrop {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

/* Loading skeleton */
.skeleton {
  background: linear-gradient(90deg, rgb(var(--slate-3)) 25%, rgb(var(--slate-4)) 50%, rgb(var(--slate-3)) 75%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
  border-radius: 8px;
}
